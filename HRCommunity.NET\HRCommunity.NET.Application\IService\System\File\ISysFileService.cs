
using HRCommunity.NET.Application.Service;

namespace HRCommunity.NET.Application.IService;
public interface ISysFileService: ITransient
{

    /// <summary>
    /// 获取文件分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<SysFile>> Page(PageFileInput input);

    /// <summary>
    /// 上传文件
    /// </summary>
    /// <param name="file"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    Task<FileOutput> UploadFile(IFormFile file, string? path);

    /// <summary>
    /// 上传视频文件
    /// </summary>
    /// <param name="file"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    FileOutput UploadVideoFile(IFormFile file, string? path);

    /// <summary>
    /// 上传多文件
    /// </summary>
    /// <param name="files"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    Task<List<FileOutput>> UploadFiles(List<IFormFile> files, string? path);

    /// <summary>
    /// 下载文件(文件流)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> DownloadFile(FileInput input);

    /// <summary>
    /// 下载文件(文件流)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> DownloadFile(DownLoadFileInput input);

    /// <summary>
    /// 批量下载文件(文件流)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> DownloadFileBatch(List<DownLoadFileInput> input);

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DeleteFile(DeleteFileInput input);


    /// <summary>
    /// 上传头像
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    Task<FileOutput> UploadAdminAvatar(IFormFile file);


    /// <summary>
    /// 上传用户头像
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    Task<FileOutput> UploadUserAvatar(IFormFile file);

    /// <summary>
    /// 上传电子签名
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    Task<FileOutput> UploadSignature(IFormFile file);

    /// <summary>
    /// 根据链接生成带签名的URL，并302(链接有效期5分钟)
    /// </summary>
    /// <param name="ossObjectLink"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    string GenerateSignedUrlAndRedirect(string ossObjectLink, string fileName);

    /// <summary>
    /// OSS在线预览
    /// 根据链接生成带签名的URL，并重命名返回签名URL
    /// </summary>
    /// <param name="ossObjectLink">oss链接，非全链接</param>
    /// <param name="fileName">文件名称</param>
    /// <returns></returns>
    string FilePreview(string ossObjectLink, string fileName);

    /// <summary>
    /// 设置oss链接访问权限
    /// </summary>
    /// <param name="ossObjectLink"></param>
    void getSetBucketAcl(string ossObjectLink);

    Task UploadVideoFileWithProgress(IFormFile file, string path);

    /// <summary>
    /// 图片合并生成pdf
    /// </summary>
    /// <param name="input"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    Task<SysFile> ImageConvertToPdf(List<DownLoadFileInput> input, string fileName);

    /// <summary>
    /// 从字节数组上传文件到OSS
    /// </summary>
    /// <param name="fileBytes">文件字节数组</param>
    /// <param name="fileName">文件名</param>
    /// <param name="path">存储路径</param>
    /// <returns></returns>
    Task<SysFile> UploadFileFromBytes(byte[] fileBytes, string fileName, string path);

    /// <summary>
    /// 根据文件id获取文件信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<FileOutput> GetFile(long id);

    /// <summary>
    /// WebOffice 在线预览（通过文件ID）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<WebOfficePreviewOutput> WebOfficePreview(WebOfficePreviewInput input);

    /// <summary>
    /// WebOffice 在线预览（通过文件路径）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<WebOfficePreviewOutput> WebOfficePreviewByPath(WebOfficePreviewByPathInput input);
}
